// 弹出窗口脚本

document.addEventListener('DOMContentLoaded', async () => {
  // 加载保存的设置
  const settings = await chrome.storage.sync.get({
    model: 'qwen3:4b',
    ollamaUrl: 'http://localhost:11434'
  });
  
  document.getElementById('model').value = settings.model;
  document.getElementById('ollamaUrl').value = settings.ollamaUrl;
  
  // 保存设置按钮
  document.getElementById('saveBtn').addEventListener('click', async () => {
    const model = document.getElementById('model').value;
    const ollamaUrl = document.getElementById('ollamaUrl').value;
    
    try {
      await chrome.storage.sync.set({
        model: model,
        ollamaUrl: ollamaUrl
      });
      
      showStatus('设置已保存！', 'success');
    } catch (error) {
      showStatus('保存失败: ' + error.message, 'error');
    }
  });
  
  // 测试连接按钮
  document.getElementById('testBtn').addEventListener('click', async () => {
    const ollamaUrl = document.getElementById('ollamaUrl').value;
    const model = document.getElementById('model').value;
    
    showStatus('正在测试连接...', 'success');
    
    try {
      // 测试Ollama服务是否可用
      const response = await fetch(`${ollamaUrl}/api/tags`);
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      const availableModels = data.models || [];
      const modelExists = availableModels.some(m => m.name === model);
      
      if (modelExists) {
        showStatus(`✅ 连接成功！模型 ${model} 可用`, 'success');
      } else {
        showStatus(`⚠️ 连接成功，但模型 ${model} 不存在。可用模型: ${availableModels.map(m => m.name).join(', ')}`, 'error');
      }
      
    } catch (error) {
      showStatus(`❌ 连接失败: ${error.message}`, 'error');
    }
  });
});

function showStatus(message, type) {
  const statusDiv = document.getElementById('status');
  statusDiv.textContent = message;
  statusDiv.className = `status ${type}`;
  statusDiv.style.display = 'block';
  
  // 3秒后隐藏状态消息
  setTimeout(() => {
    statusDiv.style.display = 'none';
  }, 3000);
}
