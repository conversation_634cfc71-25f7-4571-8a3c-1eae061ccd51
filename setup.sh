#!/bin/bash

echo "================================"
echo "   Ollama翻译助手 安装向导"
echo "================================"
echo

echo "正在检查Ollama服务状态..."
if curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo "✅ Ollama服务正在运行"
else
    echo "❌ Ollama服务未运行"
    echo
    echo "请先安装并启动Ollama服务："
    echo "1. 运行: curl -fsSL https://ollama.ai/install.sh | sh"
    echo "2. 启动服务: ollama serve"
    echo "3. 下载模型: ollama pull qwen2.5:7b"
    echo
    read -p "按Enter键继续..."
    exit 1
fi

echo
echo "正在检查可用模型..."
if curl -s http://localhost:11434/api/tags | grep -q "qwen3:4b"; then
    echo "✅ 推荐模型 qwen3:4b 已安装"
else
    echo "⚠️  推荐模型 qwen3:4b 未找到"
    echo
    read -p "是否现在下载模型？(y/n): " download
    if [[ $download == "y" || $download == "Y" ]]; then
        echo "正在下载模型，请稍候..."
        if ollama pull qwen3:4b; then
            echo "✅ 模型下载完成"
        else
            echo "❌ 模型下载失败"
        fi
    fi
fi

echo
echo "正在生成图标文件..."
if command -v python3 > /dev/null; then
    python3 generate_icons.py
elif command -v python > /dev/null; then
    python generate_icons.py
else
    echo "⚠️  未找到Python，请手动运行: python generate_icons.py"
fi

if [ -f "icons/icon16.png" ]; then
    echo "✅ 图标文件生成完成"
else
    echo "⚠️  图标生成可能失败，请检查Python和PIL是否安装"
fi

echo
echo "================================"
echo "   安装步骤："
echo "================================"
echo "1. 打开Chrome浏览器"
echo "2. 访问 chrome://extensions/"
echo "3. 开启"开发者模式""
echo "4. 点击"加载已解压的扩展程序""
echo "5. 选择当前文件夹"
echo "6. 扩展安装完成！"
echo
echo "使用方法："
echo "- 在网页中选中文字"
echo "- 点击出现的翻译按钮"
echo "- 查看翻译结果"
echo
read -p "按Enter键继续..."
