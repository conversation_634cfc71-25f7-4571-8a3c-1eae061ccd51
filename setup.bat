@echo off
chcp 65001 >nul
echo ================================
echo   Ollama翻译助手 安装向导
echo ================================
echo.

echo 正在检查Ollama服务状态...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Ollama服务正在运行
) else (
    echo ❌ Ollama服务未运行
    echo.
    echo 请先安装并启动Ollama服务：
    echo 1. 访问 https://ollama.ai 下载安装Ollama
    echo 2. 在命令行运行: ollama serve
    echo 3. 下载模型: ollama pull qwen2.5:7b
    echo.
    pause
    exit /b 1
)

echo.
echo 正在检查可用模型...
curl -s http://localhost:11434/api/tags | findstr "qwen2.5:7b" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 默认模型 qwen2.5:7b 已安装
) else (
    echo ⚠️  默认模型 qwen2.5:7b 未找到
    echo.
    set /p download="是否现在下载模型？(y/n): "
    if /i "%download%"=="y" (
        echo 正在下载模型，请稍候...
        ollama pull qwen2.5:7b
        if %errorlevel% equ 0 (
            echo ✅ 模型下载完成
        ) else (
            echo ❌ 模型下载失败
        )
    )
)

echo.
echo 正在打开图标生成页面...
start create_icons.html

echo.
echo ================================
echo   安装步骤：
echo ================================
echo 1. 在打开的页面中下载图标文件到 icons 文件夹
echo 2. 打开Chrome浏览器
echo 3. 访问 chrome://extensions/
echo 4. 开启"开发者模式"
echo 5. 点击"加载已解压的扩展程序"
echo 6. 选择当前文件夹
echo 7. 扩展安装完成！
echo.
echo 使用方法：
echo - 在网页中选中文字
echo - 点击出现的翻译按钮
echo - 查看翻译结果
echo.
pause
