@echo off
chcp 65001 >nul
echo ================================
echo   Ollama翻译助手 安装向导
echo ================================
echo.

echo 正在检查Ollama服务状态...
curl -s http://localhost:11434/api/tags >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Ollama服务正在运行
) else (
    echo ❌ Ollama服务未运行
    echo.
    echo 请先安装并启动Ollama服务：
    echo 1. 访问 https://ollama.ai 下载安装Ollama
    echo 2. 在命令行运行: ollama serve
    echo 3. 下载模型: ollama pull qwen2.5:7b
    echo.
    pause
    exit /b 1
)

echo.
echo 正在检查可用模型...
curl -s http://localhost:11434/api/tags | findstr "qwen3:4b" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 推荐模型 qwen3:4b 已安装
) else (
    echo ⚠️  推荐模型 qwen3:4b 未找到
    echo.
    set /p download="是否现在下载模型？(y/n): "
    if /i "%download%"=="y" (
        echo 正在下载模型，请稍候...
        ollama pull qwen3:4b
        if %errorlevel% equ 0 (
            echo ✅ 模型下载完成
        ) else (
            echo ❌ 模型下载失败
        )
    )
)

echo.
echo 正在生成图标文件...
python generate_icons.py
if %errorlevel% equ 0 (
    echo ✅ 图标文件生成完成
) else (
    echo ⚠️  图标生成失败，请手动运行: python generate_icons.py
)

echo.
echo ================================
echo   安装步骤：
echo ================================
echo 1. 打开Chrome浏览器
echo 2. 访问 chrome://extensions/
echo 3. 开启"开发者模式"
echo 4. 点击"加载已解压的扩展程序"
echo 5. 选择当前文件夹
echo 6. 扩展安装完成！
echo.
echo 使用方法：
echo - 在网页中选中文字
echo - 点击出现的翻译按钮
echo - 查看翻译结果
echo.
pause
