<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <style>
    body {
      width: 300px;
      padding: 16px;
      font-family: Arial, sans-serif;
      margin: 0;
    }
    
    .header {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .header h1 {
      margin: 0;
      font-size: 18px;
      color: #333;
    }
    
    .setting-group {
      margin-bottom: 16px;
    }
    
    .setting-group label {
      display: block;
      margin-bottom: 4px;
      font-weight: bold;
      color: #555;
    }
    
    .setting-group input, .setting-group select {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
      box-sizing: border-box;
    }
    
    .setting-group input:focus, .setting-group select:focus {
      outline: none;
      border-color: #4CAF50;
    }
    
    .save-btn {
      width: 100%;
      background: #4CAF50;
      color: white;
      border: none;
      padding: 10px;
      border-radius: 4px;
      font-size: 14px;
      cursor: pointer;
      margin-top: 10px;
    }
    
    .save-btn:hover {
      background: #45a049;
    }
    
    .status {
      margin-top: 10px;
      padding: 8px;
      border-radius: 4px;
      text-align: center;
      font-size: 12px;
    }
    
    .status.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }
    
    .status.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }
    
    .test-btn {
      width: 100%;
      background: #2196F3;
      color: white;
      border: none;
      padding: 8px;
      border-radius: 4px;
      font-size: 12px;
      cursor: pointer;
      margin-top: 8px;
    }
    
    .test-btn:hover {
      background: #1976D2;
    }
    
    .usage-info {
      background: #f0f8ff;
      padding: 10px;
      border-radius: 4px;
      font-size: 12px;
      color: #666;
      margin-top: 16px;
      border-left: 3px solid #2196F3;
    }
  </style>
</head>
<body>
  <div class="header">
    <h1>🌐 Ollama翻译设置</h1>
  </div>
  
  <div class="setting-group">
    <label for="ollamaUrl">Ollama服务地址:</label>
    <input type="text" id="ollamaUrl" placeholder="http://localhost:11434">
  </div>
  
  <div class="setting-group">
    <label for="model">翻译模型:</label>
    <select id="model">
      <option value="qwen3:4b">qwen3:4b (推荐)</option>
      <option value="qwen3:14b">qwen3:14b</option>
      <option value="qwen3:8b">qwen3:8b</option>
      <option value="qwen2.5:1.5b">qwen2.5:1.5b</option>
      <option value="qwen-1.8b-q8_0">qwen-1.8b-q8_0</option>
      <option value="qwen2.5:7b">qwen2.5:7b</option>
      <option value="qwen2.5:14b">qwen2.5:14b</option>
      <option value="qwen2.5:32b">qwen2.5:32b</option>
      <option value="llama3.1:8b">llama3.1:8b</option>
      <option value="llama3.1:70b">llama3.1:70b</option>
      <option value="gemma2:9b">gemma2:9b</option>
      <option value="mistral:7b">mistral:7b</option>
    </select>
  </div>
  
  <button class="test-btn" id="testBtn">测试连接</button>
  <button class="save-btn" id="saveBtn">保存设置</button>
  
  <div id="status" class="status" style="display: none;"></div>
  
  <div class="usage-info">
    <strong>使用方法:</strong><br>
    1. 确保Ollama服务正在运行<br>
    2. 在任意网页选中文字<br>
    3. 点击出现的翻译按钮<br>
    4. 查看翻译结果
  </div>
  
  <script src="popup.js"></script>
</body>
</html>
