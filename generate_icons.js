// Node.js脚本用于生成PNG图标文件
const fs = require('fs');
const { createCanvas } = require('canvas');

function createIcon(size) {
    const canvas = createCanvas(size, size);
    const ctx = canvas.getContext('2d');
    
    // 清除画布
    ctx.clearRect(0, 0, size, size);
    
    // 背景渐变
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, '#4CAF50');
    gradient.addColorStop(1, '#45a049');
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, size, size);
    
    // 绘制地球图标
    ctx.fillStyle = 'white';
    ctx.strokeStyle = 'white';
    ctx.lineWidth = Math.max(1, size / 32);
    
    const centerX = size / 2;
    const centerY = size / 2;
    const radius = size * 0.3;
    
    // 绘制圆形
    ctx.beginPath();
    ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
    ctx.stroke();
    
    // 绘制经线
    ctx.beginPath();
    ctx.ellipse(centerX, centerY, radius * 0.5, radius, 0, 0, 2 * Math.PI);
    ctx.stroke();
    
    // 绘制纬线
    ctx.beginPath();
    ctx.ellipse(centerX, centerY, radius, radius * 0.5, 0, 0, 2 * Math.PI);
    ctx.stroke();
    
    // 绘制中心线
    ctx.beginPath();
    ctx.moveTo(centerX - radius, centerY);
    ctx.lineTo(centerX + radius, centerY);
    ctx.stroke();
    
    // 添加翻译符号 "译"
    if (size >= 48) {
        ctx.font = `${size * 0.15}px Arial`;
        ctx.fillStyle = 'white';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('译', centerX, centerY + radius + size * 0.15);
    }
    
    return canvas;
}

// 创建icons目录
if (!fs.existsSync('icons')) {
    fs.mkdirSync('icons');
}

// 生成不同尺寸的图标
const sizes = [16, 48, 128];
sizes.forEach(size => {
    const canvas = createIcon(size);
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync(`icons/icon${size}.png`, buffer);
    console.log(`Generated icon${size}.png`);
});

console.log('All icons generated successfully!');
