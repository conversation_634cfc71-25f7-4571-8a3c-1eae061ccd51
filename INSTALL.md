# 🚀 快速安装指南

## 前置要求

1. **Ollama服务**：确保已安装并运行
   ```bash
   # 启动Ollama服务
   ollama serve
   
   # 下载推荐模型
   ollama pull qwen2.5:7b
   ```

2. **Python环境**（用于生成图标）：
   ```bash
   # 安装PIL库
   pip install Pillow
   ```

## 🎯 一键安装

### Windows用户
```cmd
# 双击运行
setup.bat
```

### Linux/Mac用户
```bash
# 运行安装脚本
./setup.sh
```

## 📋 手动安装步骤

1. **生成图标**：
   ```bash
   python generate_icons.py
   ```

2. **安装扩展**：
   - 打开Chrome浏览器
   - 访问 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目文件夹

3. **配置设置**：
   - 点击扩展图标
   - 确认Ollama服务地址
   - 选择翻译模型
   - 测试连接

## 🧪 测试功能

打开 `test.html` 文件，选中文字测试翻译功能。

## ❗ 常见问题

**图标加载失败**：
- 运行 `python generate_icons.py` 生成图标文件

**翻译失败**：
- 确认Ollama服务正在运行：`curl http://localhost:11434/api/tags`
- 检查模型是否已下载：`ollama list`

**连接失败**：
- 检查防火墙设置
- 确认端口11434未被占用

## 📞 获取帮助

如果遇到问题，请检查：
1. Ollama服务状态
2. 模型是否已下载
3. Chrome扩展是否正确加载
4. 浏览器控制台错误信息
