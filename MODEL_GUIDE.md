# 🤖 模型选择指南

根据你已下载的模型，这里是使用建议：

## 📊 你的可用模型

| 模型名称 | 大小 | 推荐用途 | 速度 | 质量 |
|---------|------|----------|------|------|
| **qwen3:4b** | 2.5 GB | 🌟 **日常翻译推荐** | ⚡⚡⚡ | ⭐⭐⭐ |
| qwen3:8b | 5.2 GB | 高质量翻译 | ⚡⚡ | ⭐⭐⭐⭐ |
| qwen3:14b | 9.3 GB | 专业翻译 | ⚡ | ⭐⭐⭐⭐⭐ |
| qwen2.5:1.5b | 986 MB | 快速翻译 | ⚡⚡⚡⚡ | ⭐⭐ |
| qwen-1.8b-q8_0 | 15 GB | 量化版本 | ⚡⚡ | ⭐⭐⭐ |

## 🎯 使用建议

### 🌟 推荐配置：qwen3:4b
- **最佳平衡**：速度与质量的完美平衡
- **适用场景**：日常网页翻译、文档翻译
- **响应速度**：通常1-3秒
- **内存占用**：约3-4GB

### ⚡ 快速翻译：qwen2.5:1.5b
- **超快响应**：适合快速浏览时使用
- **适用场景**：简单词汇、短句翻译
- **响应速度**：通常0.5-1秒
- **内存占用**：约1-2GB

### 🎓 高质量翻译：qwen3:8b
- **更好质量**：复杂句子、专业术语
- **适用场景**：学术文献、技术文档
- **响应速度**：通常2-5秒
- **内存占用**：约5-6GB

### 🏆 专业翻译：qwen3:14b
- **最高质量**：文学作品、正式文档
- **适用场景**：重要文件、创意内容
- **响应速度**：通常3-8秒
- **内存占用**：约8-10GB

## ⚙️ 切换模型

1. 点击Chrome扩展图标
2. 在"翻译模型"下拉菜单中选择
3. 点击"保存设置"
4. 可以点击"测试连接"验证模型可用性

## 💡 性能优化建议

### 根据使用场景选择：

**📱 移动设备/低配置电脑**
```
推荐：qwen2.5:1.5b 或 qwen3:4b
原因：内存占用小，响应快
```

**💻 普通办公电脑**
```
推荐：qwen3:4b 或 qwen3:8b
原因：性能均衡，质量较好
```

**🖥️ 高性能工作站**
```
推荐：qwen3:8b 或 qwen3:14b
原因：充分利用硬件，获得最佳质量
```

### 根据翻译内容选择：

**📰 新闻、博客、社交媒体**
```
推荐：qwen3:4b
原因：日常用语，速度重要
```

**📚 学术论文、技术文档**
```
推荐：qwen3:8b 或 qwen3:14b
原因：专业术语多，需要准确性
```

**💬 聊天、评论、简单文本**
```
推荐：qwen2.5:1.5b 或 qwen3:4b
原因：快速理解即可
```

## 🔧 故障排除

**模型加载慢？**
- 首次使用模型时需要加载到内存
- 后续使用会更快
- 考虑使用更小的模型

**翻译质量不满意？**
- 尝试使用更大的模型（qwen3:8b → qwen3:14b）
- 检查原文是否有特殊格式或术语

**内存不足？**
- 使用更小的模型（qwen3:14b → qwen3:4b → qwen2.5:1.5b）
- 关闭其他占用内存的应用

## 📈 模型更新

定期检查是否有新版本：
```bash
# 查看可用更新
ollama list

# 更新模型
ollama pull qwen3:4b
```

---

**当前默认配置：qwen3:4b** - 为你的系统优化的最佳选择！
