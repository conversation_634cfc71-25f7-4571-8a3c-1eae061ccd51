# Ollama翻译助手 Chrome扩展

一个使用本地Ollama大模型进行文字翻译的Chrome浏览器扩展。

## 功能特点

- 🌐 选中网页文字即可快速翻译
- 🤖 使用本地Ollama大模型，保护隐私
- 🎯 智能识别语言：中文翻译成英文，英文翻译成中文
- ⚡ 快速响应，无需等待
- 🎨 简洁美观的用户界面

## 安装要求

1. **Ollama服务**: 确保本地已安装并运行Ollama服务
   ```bash
   # 安装Ollama (如果还没安装)
   curl -fsSL https://ollama.ai/install.sh | sh
   
   # 启动Ollama服务
   ollama serve
   
   # 下载默认模型 (qwen2.5:7b)
   ollama pull qwen2.5:7b
   ```

2. **Chrome浏览器**: 版本88或更高

## 安装步骤

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 扩展安装完成！

## 使用方法

1. **配置设置**:
   - 点击扩展图标打开设置面板
   - 确认Ollama服务地址 (默认: `http://localhost:11434`)
   - 选择翻译模型 (默认: `qwen2.5:7b`)
   - 点击"测试连接"确保服务正常
   - 保存设置

2. **翻译文字**:
   - 在任意网页选中要翻译的文字
   - 点击出现的"🌐 翻译"按钮
   - 在弹出的窗口中查看翻译结果

## 支持的模型

扩展支持多种Ollama模型，包括：
- qwen2.5:7b (默认推荐)
- qwen2.5:14b
- qwen2.5:32b
- llama3.1:8b
- llama3.1:70b
- gemma2:9b
- mistral:7b

## 文件结构

```
├── manifest.json          # 扩展配置文件
├── content.js             # 内容脚本 - 处理页面交互
├── content.css            # 样式文件
├── background.js          # 后台脚本
├── popup.html             # 设置页面HTML
├── popup.js               # 设置页面脚本
├── icons/                 # 图标文件夹
└── README.md              # 说明文档
```

## 故障排除

### 翻译失败常见原因：

1. **Ollama服务未启动**
   ```bash
   ollama serve
   ```

2. **模型未下载**
   ```bash
   ollama pull qwen2.5:7b
   ```

3. **端口被占用**
   - 检查Ollama是否在默认端口11434运行
   - 或在设置中修改服务地址

4. **网络连接问题**
   - 确保localhost连接正常
   - 检查防火墙设置

### 检查Ollama状态：
```bash
# 查看已安装的模型
ollama list

# 查看服务状态
curl http://localhost:11434/api/tags
```

## 开发说明

这是一个Manifest V3的Chrome扩展，主要组件：

- **Content Script**: 监听文字选择，显示翻译按钮和结果
- **Background Script**: 处理API调用和设置管理
- **Popup**: 提供设置界面和连接测试

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
