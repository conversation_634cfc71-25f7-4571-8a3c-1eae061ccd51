<!DOCTYPE html>
<html>
<head>
    <title>创建扩展图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .icon-container { margin: 20px 0; }
        canvas { border: 1px solid #ccc; margin: 10px; }
    </style>
</head>
<body>
    <h1>Chrome扩展图标生成器</h1>
    <p>这个页面会生成扩展所需的图标文件</p>
    
    <div class="icon-container">
        <h3>16x16 图标</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
        <button onclick="downloadIcon('icon16', 'icon16.png')">下载 16x16</button>
    </div>
    
    <div class="icon-container">
        <h3>48x48 图标</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
        <button onclick="downloadIcon('icon48', 'icon48.png')">下载 48x48</button>
    </div>
    
    <div class="icon-container">
        <h3>128x128 图标</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
        <button onclick="downloadIcon('icon128', 'icon128.png')">下载 128x128</button>
    </div>

    <script>
        function createIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 背景渐变
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#45a049');
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 绘制地球图标
            ctx.fillStyle = 'white';
            ctx.strokeStyle = 'white';
            ctx.lineWidth = Math.max(1, size / 32);
            
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.3;
            
            // 绘制圆形
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 绘制经线
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radius * 0.5, radius, 0, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 绘制纬线
            ctx.beginPath();
            ctx.ellipse(centerX, centerY, radius, radius * 0.5, 0, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 绘制中心线
            ctx.beginPath();
            ctx.moveTo(centerX - radius, centerY);
            ctx.lineTo(centerX + radius, centerY);
            ctx.stroke();
            
            // 添加翻译符号 "译"
            if (size >= 48) {
                ctx.font = `${size * 0.15}px Arial`;
                ctx.fillStyle = 'white';
                ctx.textAlign = 'center';
                ctx.textBaseline = 'middle';
                ctx.fillText('译', centerX, centerY + radius + size * 0.15);
            }
        }
        
        function downloadIcon(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }
        
        // 创建所有图标
        createIcon('icon16', 16);
        createIcon('icon48', 48);
        createIcon('icon128', 128);
    </script>
</body>
</html>
