// 简化版本的content script - 用于调试
let translateButton = null;

// 创建翻译按钮
function createTranslateButton() {
  if (translateButton) {
    translateButton.remove();
  }
  
  translateButton = document.createElement('div');
  translateButton.id = 'ollama-translate-btn';
  translateButton.innerHTML = '🌐 翻译';
  translateButton.style.cssText = `
    position: absolute;
    background: #4CAF50;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    z-index: 10000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    user-select: none;
    font-family: Arial, sans-serif;
  `;
  
  translateButton.addEventListener('click', async (event) => {
    const selectedText = event.target.dataset.selectedText;
    if (!selectedText) return;
    
    // 使用简单的alert显示翻译结果
    alert('正在翻译: ' + selectedText);
    
    try {
      // 获取设置
      const settings = await chrome.storage.sync.get({
        model: 'qwen3:4b',
        ollamaUrl: 'http://localhost:11434'
      });
      
      // 调用Ollama API
      const response = await fetch(`${settings.ollamaUrl}/api/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: settings.model,
          prompt: `请将以下文字翻译成中文，如果原文是中文则翻译成英文。只返回翻译结果，不要添加任何解释：\n\n${selectedText}`,
          stream: false
        })
      });
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      const translation = data.response || '翻译失败';
      
      // 使用alert显示翻译结果
      alert(`原文: ${selectedText}\n\n翻译: ${translation}`);
      
    } catch (error) {
      alert(`翻译失败: ${error.message}`);
    }
    
    // 隐藏按钮
    if (translateButton) {
      translateButton.remove();
      translateButton = null;
    }
  });
  
  document.body.appendChild(translateButton);
  return translateButton;
}

// 处理文字选择
function handleTextSelection() {
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();
  
  if (selectedText.length > 0) {
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    
    const button = createTranslateButton();
    button.style.left = (rect.left + window.scrollX) + 'px';
    button.style.top = (rect.bottom + window.scrollY + 5) + 'px';
    
    // 存储选中的文字
    button.dataset.selectedText = selectedText;
    
    // 3秒后自动隐藏按钮
    setTimeout(() => {
      if (button && button.parentNode) {
        button.remove();
      }
    }, 3000);
  } else {
    // 如果没有选中文字，移除按钮
    if (translateButton) {
      translateButton.remove();
      translateButton = null;
    }
  }
}

// 监听文字选择事件
document.addEventListener('mouseup', handleTextSelection);
document.addEventListener('keyup', handleTextSelection);

// 点击其他地方时隐藏按钮
document.addEventListener('click', (event) => {
  if (translateButton && !translateButton.contains(event.target)) {
    translateButton.remove();
    translateButton = null;
  }
});

console.log('Ollama翻译助手 - 简化版本已加载');
