// 后台脚本 - 处理扩展的后台逻辑

// 安装时设置默认配置
chrome.runtime.onInstalled.addListener(() => {
  chrome.storage.sync.set({
    model: 'qwen3:4b',
    ollamaUrl: 'http://localhost:11434'
  });
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'translate') {
    handleTranslation(request.text, sendResponse);
    return true; // 保持消息通道开放以进行异步响应
  }
});

// 处理翻译请求
async function handleTranslation(text, sendResponse) {
  try {
    // 获取设置
    const settings = await chrome.storage.sync.get({
      model: 'qwen3:4b',
      ollamaUrl: 'http://localhost:11434'
    });
    
    // 调用Ollama API
    const response = await fetch(`${settings.ollamaUrl}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: settings.model,
        prompt: `请将以下文字翻译成中文，如果原文是中文则翻译成英文。只返回翻译结果，不要添加任何解释：\n\n${text}`,
        stream: false
      })
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    sendResponse({ success: true, translation: data.response });
    
  } catch (error) {
    console.error('翻译错误:', error);
    sendResponse({ success: false, error: error.message });
  }
}
