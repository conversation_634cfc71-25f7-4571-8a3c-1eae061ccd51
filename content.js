// 翻译弹出框元素
let translationPopup = null;
let translateButton = null;

// 创建翻译按钮
function createTranslateButton() {
  if (translateButton) {
    translateButton.remove();
  }
  
  translateButton = document.createElement('div');
  translateButton.id = 'ollama-translate-btn';
  translateButton.innerHTML = '🌐 翻译';
  translateButton.style.cssText = `
    position: absolute;
    background: #4CAF50;
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    z-index: 10000;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    user-select: none;
    font-family: Arial, sans-serif;
  `;
  
  translateButton.addEventListener('click', (event) => {
    console.log('翻译按钮被点击');
    handleTranslate(event);
  });
  document.body.appendChild(translateButton);
  
  return translateButton;
}

// 创建翻译结果弹出框
function createTranslationPopup() {
  console.log('createTranslationPopup 被调用');
  if (translationPopup) {
    console.log('移除现有弹出框');
    translationPopup.remove();
  }

  translationPopup = document.createElement('div');
  translationPopup.id = 'ollama-translation-popup';
  translationPopup.innerHTML = `
    <div class="popup-header">
      <span>翻译结果</span>
      <button class="close-btn">×</button>
    </div>
    <div class="popup-content">
      <div class="original-text"></div>
      <div class="translation-result">正在翻译...</div>
    </div>
  `;

  console.log('将弹出框添加到页面');
  document.body.appendChild(translationPopup);
  console.log('弹出框已添加到页面:', translationPopup);

  // 确保弹出框可见
  translationPopup.style.display = 'block';
  translationPopup.style.visibility = 'visible';
  translationPopup.style.opacity = '1';
  
  // 添加关闭按钮事件
  translationPopup.querySelector('.close-btn').addEventListener('click', () => {
    translationPopup.remove();
    translationPopup = null;
  });
  
  return translationPopup;
}

// 处理文字选择
function handleTextSelection() {
  const selection = window.getSelection();
  const selectedText = selection.toString().trim();
  
  if (selectedText.length > 0) {
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    
    const button = createTranslateButton();
    button.style.left = (rect.left + window.scrollX) + 'px';
    button.style.top = (rect.bottom + window.scrollY + 5) + 'px';
    
    // 存储选中的文字
    button.dataset.selectedText = selectedText;
    
    // 3秒后自动隐藏按钮
    setTimeout(() => {
      if (button && button.parentNode) {
        button.remove();
      }
    }, 3000);
  } else {
    // 如果没有选中文字，移除按钮
    if (translateButton) {
      translateButton.remove();
      translateButton = null;
    }
  }
}

// 处理翻译请求
async function handleTranslate(event) {
  console.log('handleTranslate 被调用');
  const selectedText = event.target.dataset.selectedText;
  console.log('选中的文字:', selectedText);

  // 添加一个alert来确保函数被调用
  alert('翻译按钮被点击！选中的文字: ' + selectedText);

  if (!selectedText) {
    console.log('没有选中文字，返回');
    return;
  }

  // 创建弹出框
  console.log('正在创建弹出框...');
  const popup = createTranslationPopup();
  console.log('弹出框创建完成:', popup);
  popup.querySelector('.original-text').textContent = `原文: ${selectedText}`;
  
  // 隐藏翻译按钮
  if (translateButton) {
    translateButton.remove();
    translateButton = null;
  }
  
  // 先显示一个测试消息，确保弹出框工作正常
  const translationResult = popup.querySelector('.translation-result');
  translationResult.textContent = '正在翻译...';

  try {
    // 获取设置
    console.log('正在获取设置...');
    const settings = await chrome.storage.sync.get({
      model: 'qwen3:4b',
      ollamaUrl: 'http://localhost:11434'
    });
    console.log('获取到设置:', settings);

    // 调用Ollama API
    console.log('正在调用Ollama API...');
    const response = await fetch(`${settings.ollamaUrl}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: settings.model,
        prompt: `请将以下文字翻译成中文，如果原文是中文则翻译成英文。只返回翻译结果，不要添加任何解释：\n\n${selectedText}`,
        stream: false
      })
    });

    console.log('API响应状态:', response.status);
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('API响应数据:', data);
    translationResult.textContent = data.response || '翻译失败';

  } catch (error) {
    console.error('翻译错误:', error);
    translationResult.textContent = `翻译失败: ${error.message}`;
  }
}

// 监听文字选择事件
document.addEventListener('mouseup', handleTextSelection);
document.addEventListener('keyup', handleTextSelection);

// 点击其他地方时隐藏按钮
document.addEventListener('click', (event) => {
  if (translateButton && !translateButton.contains(event.target)) {
    translateButton.remove();
    translateButton = null;
  }
});
