/* 翻译弹出框样式 */
#ollama-translation-popup {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  width: 400px !important;
  max-width: 90vw !important;
  background: white !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3) !important;
  z-index: 999999 !important;
  font-family: Arial, sans-serif !important;
  overflow: hidden !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

#ollama-translation-popup .popup-header {
  background: #4CAF50;
  color: white;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

#ollama-translation-popup .close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

#ollama-translation-popup .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

#ollama-translation-popup .popup-content {
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

#ollama-translation-popup .original-text {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #666;
  border-left: 3px solid #4CAF50;
}

#ollama-translation-popup .translation-result {
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  min-height: 20px;
}

/* 翻译按钮悬停效果 */
#ollama-translate-btn:hover {
  background: #45a049 !important;
  transform: scale(1.05);
  transition: all 0.2s ease;
}

/* 防止与页面样式冲突 */
#ollama-translation-popup * {
  box-sizing: border-box;
}

/* 响应式设计 */
@media (max-width: 480px) {
  #ollama-translation-popup {
    width: 95vw;
    margin: 10px;
  }
  
  #ollama-translation-popup .popup-content {
    padding: 12px;
  }
}
