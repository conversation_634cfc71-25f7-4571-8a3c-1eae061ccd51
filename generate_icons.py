#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成Chrome扩展图标的Python脚本
需要安装PIL: pip install Pillow
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size):
    """创建指定尺寸的图标"""
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 绘制背景圆形
    margin = 2
    draw.ellipse([margin, margin, size-margin, size-margin], 
                fill=(76, 175, 80, 255), outline=None)
    
    # 计算中心点和半径
    center_x = size // 2
    center_y = size // 2
    radius = (size - margin * 2) // 3
    
    # 绘制地球轮廓
    line_width = max(1, size // 32)
    
    # 外圆
    draw.ellipse([center_x - radius, center_y - radius, 
                 center_x + radius, center_y + radius], 
                outline=(255, 255, 255, 255), width=line_width)
    
    # 经线（椭圆）
    ellipse_width = radius // 2
    draw.ellipse([center_x - ellipse_width, center_y - radius,
                 center_x + ellipse_width, center_y + radius],
                outline=(255, 255, 255, 255), width=line_width)
    
    # 纬线（水平椭圆）
    ellipse_height = radius // 2
    draw.ellipse([center_x - radius, center_y - ellipse_height,
                 center_x + radius, center_y + ellipse_height],
                outline=(255, 255, 255, 255), width=line_width)
    
    # 中心水平线
    draw.line([center_x - radius, center_y, center_x + radius, center_y],
              fill=(255, 255, 255, 255), width=line_width)
    
    # 如果图标足够大，添加文字
    if size >= 48:
        try:
            # 尝试使用系统字体
            font_size = size // 8
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # 如果找不到字体，使用默认字体
            font = ImageFont.load_default()
        
        # 添加"译"字
        text = "译"
        text_y = center_y + radius + size // 10
        
        # 获取文字尺寸来居中
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_x = center_x - text_width // 2
        
        draw.text((text_x, text_y), text, fill=(255, 255, 255, 255), font=font)
    
    return img

def main():
    """主函数"""
    # 创建icons目录
    if not os.path.exists('icons'):
        os.makedirs('icons')
    
    # 生成不同尺寸的图标
    sizes = [16, 48, 128]
    
    for size in sizes:
        print(f"正在生成 {size}x{size} 图标...")
        icon = create_icon(size)
        filename = f'icons/icon{size}.png'
        icon.save(filename, 'PNG')
        print(f"已保存: {filename}")
    
    print("所有图标生成完成！")

if __name__ == "__main__":
    main()
