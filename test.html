<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ollama翻译助手 - 测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #4CAF50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border-left: 4px solid #4CAF50;
            background: #f9f9f9;
        }
        
        .test-section h2 {
            color: #333;
            margin-top: 0;
        }
        
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #1976d2;
        }
        
        code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Ollama翻译助手测试页面</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>1. 确保Chrome扩展已安装并启用</p>
            <p>2. 确保Ollama服务正在运行 (<code>ollama serve</code>)</p>
            <p>3. 选中下面的文字，点击出现的翻译按钮</p>
            <p>4. 查看翻译结果是否正确显示</p>
        </div>
        
        <div class="test-section">
            <h2>中文测试文本</h2>
            <p>人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。</p>
            <p>机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。</p>
            <p class="highlight">深度学习是机器学习的一个子集，它模仿人脑的工作方式来处理数据并创建用于决策的模式。</p>
        </div>
        
        <div class="test-section">
            <h2>English Test Text</h2>
            <p>Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines that can perform tasks that typically require human intelligence.</p>
            <p>Machine learning is a subset of AI that enables computers to learn and improve from experience without being explicitly programmed.</p>
            <p class="highlight">Natural Language Processing (NLP) is a field of AI that focuses on the interaction between computers and human language.</p>
        </div>
        
        <div class="test-section">
            <h2>技术术语测试</h2>
            <p>API (Application Programming Interface) 是应用程序编程接口的缩写。</p>
            <p>JSON (JavaScript Object Notation) 是一种轻量级的数据交换格式。</p>
            <p class="highlight">REST (Representational State Transfer) 是一种软件架构风格。</p>
        </div>
        
        <div class="test-section">
            <h2>Mixed Language Test</h2>
            <p>现代的web开发通常使用JavaScript、HTML和CSS这三种核心技术。</p>
            <p>React是一个用于构建用户界面的JavaScript库，由Facebook开发。</p>
            <p class="highlight">Node.js allows developers to use JavaScript for server-side programming.</p>
        </div>
        
        <div class="test-section">
            <h2>长文本测试</h2>
            <p>
                随着互联网技术的快速发展，云计算、大数据、人工智能等新兴技术正在深刻改变着我们的生活方式和工作模式。
                企业数字化转型已成为时代发展的必然趋势，越来越多的传统企业开始拥抱数字化技术，
                通过数据驱动的决策和智能化的业务流程来提升竞争力。
                在这个过程中，如何有效地整合各种技术资源，构建稳定可靠的技术架构，
                成为了企业成功转型的关键因素。
            </p>
        </div>
        
        <div class="instructions">
            <h3>🔧 故障排除</h3>
            <p><strong>如果翻译按钮没有出现：</strong></p>
            <ul>
                <li>检查扩展是否已安装并启用</li>
                <li>刷新页面重试</li>
                <li>检查浏览器控制台是否有错误信息</li>
            </ul>
            
            <p><strong>如果翻译失败：</strong></p>
            <ul>
                <li>确认Ollama服务正在运行：<code>curl http://localhost:11434/api/tags</code></li>
                <li>检查模型是否已下载：<code>ollama list</code></li>
                <li>查看扩展设置中的服务地址是否正确</li>
            </ul>
        </div>
    </div>
</body>
</html>
